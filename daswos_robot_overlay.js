/**
 * Daswos Robot Overlay Script
 * 
 * This self-contained script creates an overlay with the Daswos robot animation
 * that can be added to any website.
 */

(function() {
    // Create and inject CSS
    const style = document.createElement('style');
    style.textContent = `
        #daswos-robot-container {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 150px;
            height: 150px;
            z-index: 9999;
            pointer-events: none;
        }
        
        #daswos-robot-canvas {
            width: 100%;
            height: 100%;
            pointer-events: auto;
        }
        
        #daswos-robot-controls {
            position: fixed;
            bottom: 10px;
            right: 180px;
            display: flex;
            flex-direction: column;
            gap: 5px;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        #daswos-robot-container:hover + #daswos-robot-controls,
        #daswos-robot-controls:hover {
            opacity: 1;
        }
        
        .daswos-robot-button {
            padding: 5px 10px;
            border: none;
            border-radius: 5px;
            background-color: #4285f4;
            color: white;
            font-weight: bold;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.3s;
        }
        
        .daswos-robot-button:hover {
            background-color: #3367d6;
        }
        
        @media (max-width: 768px) {
            #daswos-robot-container {
                width: 100px;
                height: 100px;
                bottom: 10px;
                right: 10px;
            }
            
            #daswos-robot-controls {
                right: 120px;
                bottom: 5px;
            }
            
            .daswos-robot-button {
                padding: 3px 6px;
                font-size: 10px;
            }
        }
    `;
    document.head.appendChild(style);
    
    // Create container and canvas elements
    const container = document.createElement('div');
    container.id = 'daswos-robot-container';
    
    const canvas = document.createElement('canvas');
    canvas.id = 'daswos-robot-canvas';
    container.appendChild(canvas);
    
    // Create controls
    const controls = document.createElement('div');
    controls.id = 'daswos-robot-controls';
    
    const states = ['Idle', 'Talk', 'Dance', 'Roll', 'Search'];
    states.forEach(state => {
        const button = document.createElement('button');
        button.className = 'daswos-robot-button';
        button.textContent = state;
        button.onclick = () => window.daswosRobot.setState(state.toLowerCase());
        controls.appendChild(button);
    });
    
    // Add elements to DOM
    document.body.appendChild(container);
    document.body.appendChild(controls);
    
    // Load p5.js
    const p5Script = document.createElement('script');
    p5Script.src = 'https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.4.0/p5.min.js';
    p5Script.onload = initRobotAnimation;
    document.head.appendChild(p5Script);
    
    // Initialize robot animation
    function initRobotAnimation() {
        // Robot animation code
        const sketch = (p) => {
            // Image variables for different robot views
            let robotImages = {
                front: null,
                side: null,
                threeQuarter: null,
                back: null,
                top: null
            };
            
            // Animation state variables
            let robotState = 'idle';
            let previousState = 'idle';
            let stateStartTime = 0;
            let viewTransitionProgress = 0;
            
            // Position and movement variables
            let robotX;
            let robotY;
            let targetX = 0;
            let targetY = 0;
            let isRolling = false;
            let rollDirection = 0;
            let rollSpeed = 0;
            
            // Animation effect variables
            let headRotation = 0;
            let headBobAmount = 0;
            let bodyRotation = 0;
            let bodyRotationSpeed = 0;
            let armLeftRotation = 0;
            let armRightRotation = 0;
            let legsVisible = true;
            let legsVisibility = 1;
            let eyeBlinkTime = 0;
            let isBlinking = false;
            let talkPulse = 0;
            let dancePhase = 0;
            let searchAngle = 0;
            
            // View management variables
            let currentView = 'front';
            let targetView = 'front';
            
            // Mouse interaction variables
            let lastMouseX = 0;
            let lastMouseY = 0;
            let mouseInteractionTimer = 0;
            
            // Constants
            const ROBOT_SCALE = 0.5;
            const SHADOW_OPACITY = 0.3;
            const SHADOW_SCALE_Y = 0.2;
            const SHADOW_OFFSET_Y = 20;
            
            // Preload function - create placeholder images
            p.preload = function() {
                // Create placeholder images with robot colors
                const createPlaceholderImage = (color) => {
                    const img = p.createImage(100, 100);
                    img.loadPixels();
                    for (let i = 0; i < img.width; i++) {
                        for (let j = 0; j < img.height; j++) {
                            const d = p.dist(i, j, img.width/2, img.height/2);
                            if (d < img.width/2) {
                                img.set(i, j, color);
                            }
                        }
                    }
                    img.updatePixels();
                    return img;
                };
                
                // Create placeholder images for each view
                robotImages.front = createPlaceholderImage(p.color(30, 144, 255));
                robotImages.side = createPlaceholderImage(p.color(30, 144, 255));
                robotImages.threeQuarter = createPlaceholderImage(p.color(30, 144, 255));
                robotImages.back = createPlaceholderImage(p.color(30, 144, 255));
                robotImages.top = createPlaceholderImage(p.color(30, 144, 255));
                
                // Try to load actual images
                loadRobotImages();
            };
            
            // Setup function
            p.setup = function() {
                const canvas = p.createCanvas(150, 150);
                canvas.parent('daswos-robot-canvas');
                
                // Initialize robot position to center of canvas
                robotX = p.width / 2;
                robotY = p.height / 2;
                
                // Initial entrance animation
                robotX = -50;
                targetX = p.width / 2;
                targetY = p.height / 2;
                setRobotState('roll');
                
                // Make canvas clickable
                canvas.mousePressed(canvasClicked);
            };
            
            // Draw function
            p.draw = function() {
                // Clear background with transparency
                p.clear();
                
                // Update animation based on current state
                updateAnimation();
                
                // Draw robot
                drawRobot();
                
                // Handle mouse interaction
                handleMouseInteraction();
            };
            
            // Try to load actual robot images
            function loadRobotImages() {
                const baseUrl = 'https://raw.githubusercontent.com/user/repo/main/images/';
                const imagePaths = {
                    front: 'robot_front_view.png',
                    side: 'robot_side_view.png',
                    threeQuarter: 'robot_three_quarter_view.png',
                    back: 'robot_back_view.png',
                    top: 'robot_top_view.png'
                };
                
                // Try to load each image
                Object.keys(imagePaths).forEach(view => {
                    p.loadImage(
                        baseUrl + imagePaths[view],
                        img => { robotImages[view] = img; },
                        () => console.log(`Failed to load ${view} image, using placeholder`)
                    );
                });
            }
            
            // Update animation based on current state
            function updateAnimation() {
                // Calculate time in current state
                let currentTime = p.millis();
                let timeInState = currentTime - stateStartTime;
                
                // Handle rolling movement if needed
                if (isRolling) {
                    // Calculate direction to target
                    let dx = targetX - robotX;
                    let dy = targetY - robotY;
                    let distance = p.sqrt(dx*dx + dy*dy);
                    
                    // If we're close enough to target, stop rolling
                    if (distance < 5) {
                        isRolling = false;
                        if (robotState === 'roll') {
                            setRobotState('idle');
                        }
                    } else {
                        // Update position and rotation
                        rollDirection = p.atan2(dy, dx);
                        rollSpeed = p.min(distance * 0.05, 5);
                        robotX += p.cos(rollDirection) * rollSpeed;
                        robotY += p.sin(rollDirection) * rollSpeed;
                        
                        // Rotate body based on movement (wheel rotation)
                        bodyRotationSpeed = rollSpeed * 0.2;
                        bodyRotation += bodyRotationSpeed;
                        
                        // Set appropriate view based on roll direction
                        if (p.abs(p.cos(rollDirection)) > p.abs(p.sin(rollDirection))) {
                            // Moving more horizontally
                            targetView = p.cos(rollDirection) > 0 ? 'side' : 'side';
                        } else {
                            // Moving more vertically
                            targetView = p.sin(rollDirection) > 0 ? 'threeQuarter' : 'back';
                        }
                        
                        // Hide legs during rolling
                        legsVisible = false;
                        legsVisibility = p.max(0, legsVisibility - 0.1);
                    }
                } else if (robotState !== 'roll' && !legsVisible) {
                    // Show legs when not rolling
                    legsVisible = true;
                    legsVisibility = p.min(1, legsVisibility + 0.05);
                }
                
                // State-specific updates
                switch (robotState) {
                    case 'idle':
                        // Subtle bobbing motion
                        headBobAmount = p.sin(currentTime * 0.002) * 5;
                        
                        // Occasional head rotation
                        headRotation = p.sin(currentTime * 0.001) * 0.1;
                        
                        // Subtle arm movements
                        armLeftRotation = p.sin(currentTime * 0.001) * 0.05;
                        armRightRotation = p.sin(currentTime * 0.001 + p.PI) * 0.05;
                        
                        // Default to front view in idle
                        if (!isRolling) targetView = 'front';
                        
                        // Ensure legs are visible
                        legsVisible = true;
                        legsVisibility = p.min(1, legsVisibility + 0.05);
                        break;
                        
                    case 'talk':
                        // Pulsing head effect
                        talkPulse = p.sin(currentTime * 0.01) * 0.05;
                        
                        // Head bobbing synchronized with "speech"
                        headBobAmount = p.sin(currentTime * 0.01) * 3;
                        
                        // Arm gestures synchronized with talking
                        armLeftRotation = p.sin(currentTime * 0.008) * 0.2;
                        armRightRotation = p.sin(currentTime * 0.008 + p.PI) * 0.2;
                        
                        // Always use front view when talking
                        targetView = 'front';
                        break;
                        
                    case 'dance':
                        // Update dance phase
                        dancePhase += 0.05;
                        
                        // Head bobbing with dance rhythm
                        headBobAmount = p.sin(dancePhase * 2) * 8;
                        headRotation = p.sin(dancePhase) * 0.2;
                        
                        // Arm movements with dance rhythm
                        armLeftRotation = p.sin(dancePhase) * 0.4;
                        armRightRotation = p.sin(dancePhase + p.PI) * 0.4;
                        
                        // Side-to-side body movement
                        if (!isRolling) {
                            robotX += p.sin(dancePhase) * 2;
                        }
                        
                        // Cycle through views for dancing
                        if (timeInState % 2000 < 500) {
                            targetView = 'front';
                        } else if (timeInState % 2000 < 1000) {
                            targetView = 'threeQuarter';
                        } else if (timeInState % 2000 < 1500) {
                            targetView = 'side';
                        } else {
                            targetView = 'threeQuarter';
                        }
                        break;
                        
                    case 'search':
                        // Update search angle
                        searchAngle += 0.03;
                        
                        // Head rotation for searching
                        headRotation = p.sin(searchAngle) * 0.3;
                        
                        // Subtle body movement
                        headBobAmount = p.sin(currentTime * 0.005) * 3;
                        
                        // Arm movements for searching/pointing
                        armLeftRotation = p.sin(searchAngle * 0.5) * 0.2;
                        armRightRotation = p.sin(searchAngle * 0.5 + p.PI) * 0.2;
                        
                        // Cycle through views for searching
                        if (timeInState % 3000 < 1000) {
                            targetView = 'front';
                        } else if (timeInState % 3000 < 2000) {
                            targetView = 'threeQuarter';
                        } else {
                            targetView = 'side';
                        }
                        break;
                        
                    case 'roll':
                        // Most updates handled in the rolling logic above
                        
                        // Add some head bobbing during roll
                        headBobAmount = p.sin(currentTime * 0.01) * 3;
                        
                        // Slight arm movement based on speed
                        armLeftRotation = p.sin(currentTime * 0.01) * 0.1 * (rollSpeed * 0.1);
                        armRightRotation = p.sin(currentTime * 0.01 + p.PI) * 0.1 * (rollSpeed * 0.1);
                        
                        // Hide legs during rolling
                        legsVisible = false;
                        legsVisibility = p.max(0, legsVisibility - 0.1);
                        break;
                }
            }
            
            // Draw the robot based on current state and view
            function drawRobot() {
                p.push();
                p.translate(robotX, robotY);
                
                // Draw shadow
                drawShadow();
                
                // Apply bobbing effect
                p.translate(0, headBobAmount);
                
                // Scale the robot
                p.scale(ROBOT_SCALE);
                
                // Apply body rotation for wheel effect
                if (robotState === 'roll') {
                    p.push();
                    p.rotate(bodyRotation);
                }
                
                // Draw the current view
                let currentImage = robotImages[currentView];
                
                // Apply effects based on state
                if (robotState === 'talk') {
                    // Subtle pulsing for talking
                    p.scale(1 + talkPulse);
                }
                
                // Draw the current view
                p.imageMode(p.CENTER);
                p.image(currentImage, 0, 0);
                
                if (robotState === 'roll') {
                    p.pop(); // End body rotation
                }
                
                p.pop();
            }
            
            // Draw shadow beneath the robot
            function drawShadow() {
                p.push();
                p.translate(0, SHADOW_OFFSET_Y);
                p.fill(0, 0, 0, SHADOW_OPACITY * 255);
                p.noStroke();
                p.ellipse(0, 0, 120 * ROBOT_SCALE, 30 * ROBOT_SCALE * SHADOW_SCALE_Y);
                p.pop();
            }
            
            // Handle mouse interaction with the robot
            function handleMouseInteraction() {
                // Calculate distance from mouse to robot
                let d = p.dist(p.mouseX, p.mouseY, robotX, robotY);
                
                // If mouse is near robot and moving
                if (d < 50 && (p.abs(p.mouseX - lastMouseX) > 5 || p.abs(p.mouseY - lastMouseY) > 5)) {
                    // Make robot look toward mouse
                    let angle = p.atan2(p.mouseY - robotY, p.mouseX - robotX);
                    headRotation = p.lerp(headRotation, angle * 0.2, 0.1);
                    
                    // Set appropriate view based on mouse position
                    if (p.abs(p.cos(angle)) > 0.7) {
                        // Mouse is more to the sides
                        targetView = p.cos(angle) > 0 ? 'threeQuarter' : 'threeQuarter';
                    } else {
                        // Mouse is more above/below
                        targetView = p.sin(angle) > 0 ? 'front' : 'top';
                    }
                    
                    // Reset interaction timer
                    mouseInteractionTimer = p.millis() + 1000;
                }
                
                // Store current mouse position for next frame
                lastMouseX = p.mouseX;
                lastMouseY = p.mouseY;
            }
            
            // Set the robot's animation state
            function setRobotState(state) {
                // Store previous state for transitions
                previousState = robotState;
                robotState = state;
                
                // Record start time for this state
                stateStartTime = p.millis();
                
                // Reset state-specific variables
                switch (state) {
                    case 'idle':
                        if (!isRolling) {
                            targetView = 'front';
                        }
                        break;
                        
                    case 'talk':
                        targetView = 'front';
                        talkPulse = 0;
                        break;
                        
                    case 'dance':
                        dancePhase = 0;
                        break;
                        
                    case 'search':
                        searchAngle = 0;
                        break;
                        
                    case 'roll':
                        isRolling = true;
                        // Hide legs immediately when starting to roll
                        legsVisible = false;
                        break;
                }
            }
            
            // Handle canvas clicks
            function canvasClicked() {
                // If clicking near the robot, make it do a special animation
                let d = p.dist(p.mouseX, p.mouseY, robotX, robotY);
                if (d < 50 * ROBOT_SCALE) {
                    // Quick reaction animation
                    headBobAmount = -10;
                    setTimeout(() => { headBobAmount = 0; }, 300);
                } else {
                    // Roll to where user clicked
                    targetX = p.mouseX;
                    targetY = p.mouseY;
                    setRobotState('roll');
                }
            }
            
            // Expose setState function to window object
            window.daswosRobot = {
                setState: setRobotState
            };
        };
        
        // Start the p5 sketch
        new p5(sketch);
    }
})();
